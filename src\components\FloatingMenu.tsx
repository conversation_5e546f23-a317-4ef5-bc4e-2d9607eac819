"use client";

import { useState } from "react";
import { FaBars, FaTimes } from "react-icons/fa";
import { useProductTypes } from "@/hooks/useProductTypes";

interface FloatingMenuProps {
  onCategorySelect: (productType: string | null) => void;
  selectedCategory: string | null;
}

export default function FloatingMenu({ onCategorySelect, selectedCategory }: FloatingMenuProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [hasOpened, setHasOpened] = useState(false);
  const { productTypes, loading, error, refetch } = useProductTypes();

  const toggleMenu = () => {
    const wasOpen = isOpen;
    setIsOpen(!isOpen);

    // Only fetch product types when menu is first opened
    if (!wasOpen && !hasOpened) {
      setHasOpened(true);
      refetch();
    }
  };

  const handleCategoryClick = (productType: string | null) => {
    onCategorySelect(productType);
    setIsOpen(false); // Close menu after selection
  };

  return (
    <>
      {/* Floating FaBars Button - Top Left */}
      <button
        onClick={toggleMenu}
        className="fixed top-11 left-2 sm:left-6 z-50 p-3 bg-orange-500 text-white rounded-full shadow-lg hover:bg-orange-600 transition-all duration-200 hover:scale-105"
        aria-label="Toggle category menu"
      >
        {isOpen ? <FaTimes size={20} /> : <FaBars size={20} />}
      </button>

      {/* Overlay */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40"
          onClick={() => setIsOpen(false)}
        />
      )}

      {/* Floating Menu */}
      {isOpen && (
        <div className="fixed top-21 left-2 sm:left-6 w-72 sm:w-80 max-h-[80vh] bg-white rounded-lg shadow-xl z-40 overflow-hidden flex flex-col">
          <div className="p-4 bg-orange-500 text-white flex-shrink-0">
            <h2 className="text-lg font-bold">หมวดหมู่สินค้า</h2>
          </div>

          <div
            className="flex-1 overflow-y-auto custom-scrollbar pr-1"
            style={{
              scrollbarWidth: 'thin',
              scrollbarColor: '#d1d5db #f3f4f6'
            }}
          >
            {/* All Products Option */}
            <button
              onClick={() => handleCategoryClick(null)}
              className={`w-full text-left px-5 py-3 border-b border-gray-100 transition-colors ${
                selectedCategory === null
                  ? "bg-orange-100 text-orange-800 font-medium"
                  : "text-gray-700 hover:bg-gray-50"
              }`}
            >
              📦 สินค้าทั้งหมด
            </button>

            {/* Loading State */}
            {loading && (
              <div className="flex justify-center items-center py-8">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-orange-500"></div>
                <span className="ml-2 text-gray-600">กำลังโหลด...</span>
              </div>
            )}

            {/* Error State */}
            {error && (
              <div className="p-4 bg-red-50 border-l-4 border-red-400">
                <p className="text-red-700 text-sm">
                  {error.includes('Rate limit')
                    ? 'กำลังโหลดหมวดหมู่... กรุณารอสักครู่'
                    : error}
                </p>
                {error.includes('Rate limit') && (
                  <button
                    onClick={() => refetch()}
                    className="mt-2 text-xs text-red-600 hover:text-red-800 underline"
                  >
                    ลองใหม่
                  </button>
                )}
              </div>
            )}

            {/* Product Types List */}
            {!loading && !error && (
              <div>
                {productTypes.map((productType, index) => (
                  <button
                    key={productType}
                    onClick={() => handleCategoryClick(productType)}
                    className={`w-full text-left px-4 py-3 border-b border-gray-100 transition-colors ${
                      selectedCategory === productType
                        ? "bg-orange-100 text-orange-800 font-medium"
                        : "text-gray-700 hover:bg-gray-50"
                    } ${index === productTypes.length - 1 ? 'border-b-0' : ''}`}
                  >
                    🏷️ {productType}
                  </button>
                ))}
              </div>
            )}

            {/* Empty State */}
            {!loading && !error && productTypes.length === 0 && (
              <div className="text-center text-gray-500 py-8">
                <p>ไม่พบหมวดหมู่สินค้า</p>
              </div>
            )}
          </div>
        </div>
      )}
    </>
  );
}
